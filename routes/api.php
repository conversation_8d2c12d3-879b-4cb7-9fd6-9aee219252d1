<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Middlewares\TenantDBConnection;
use App\Models\Tenants\ChsoneCreditNote;
use App\Models\Tenants\ChsoneMembersMaster;
use App\Models\WorkflowLogs;
use App\Console\Commands\Workflows\CheckWorkflow;
use Illuminate\Support\Facades\Redis;
use App\Http\Controllers\LoginController;
use App\Http\Controllers\PdfController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
 */

Route::middleware('auth:sanctum')->get('/user', fn(Request $request) => $request->user());

Route::post('/login', [LoginController::class, 'login'])->withoutMiddleware(['tenant.connection', 'request.prepare','keycloak.token','group.access', 'tenant.id']);
Route::get('/company-list', [LoginController::class, 'getCompanyList'])->withoutMiddleware(['tenant.connection', 'request.prepare','group.access','tenant.id']);

// Route::middleware('auth:api')->group(function () {
//     Route::get('/test-route', function(){
//         return "Hello";
//     });
// });

// Route::group(['prefix' => 'v2/admin', 'middleware' => ['tenant.connection', 'request.prepare', 'validate.token']], function () {
Route::group(['prefix' => 'v2/admin', 'middleware' => ['tenant.connection', 'request.prepare']], function (): void {
    Route::get('/member/list', 'App\Http\Controllers\DCOController@allottees');
    Route::get('/units/list', 'App\Http\Controllers\GeneralSettingsController@complexUnitList');
});

Route::group(['prefix' => 'admin', 'middleware' => ['tenant.connection', 'request.prepare']], function (): void {
    // STEP 1: Add the route for the new endpoint

    // Get member type
    Route::get('/member/primarymember/{unit_id}', 'App\Http\Controllers\MemberController@getPrimaryMember');

    /** COMPANY DETAILS API **/
    Route::get('/company/details', 'App\Http\Controllers\CommonController@companyDetails');

    Route::get('/onegate/migration', 'App\Http\Controllers\CommonController@migration');

    /** CREDIT ACCOUNTS APIs **/
    Route::post('/credit-accounts/creditNoteAdd', 'App\Http\Controllers\CreditAccountsController@creditNoteAdd');

    Route::get('/credit-accounts/creditNote', 'App\Http\Controllers\CreditAccountsController@creditNote');
    Route::get(
        '/credit-accounts/viewAllTransactions/{type}/{id}',
        'App\Http\Controllers\CreditAccountsController@viewMemberTransactions'
    );
    Route::get('/credit-accounts/creditNote/invoiceParticular/{id}', 'App\Http\Controllers\CreditAccountsController@creditNoteInvoiceParticular');

    Route::get('/credit-accounts/memberAdvances', 'App\Http\Controllers\CreditAccountsController@memberAdvances'); //currently not using
    Route::get('/credit-accounts/nonMemberAdvances', 'App\Http\Controllers\CreditAccountsController@nonMemberAdvances'); //currently not using
    Route::get('/credit-accounts/nonMemberAdvances/report', 'App\Http\Controllers\CreditAccountsController@nonMemberAdvances'); 
    Route::get('/credit-accounts/nonMemberAdvances/download/{type}', 'App\Http\Controllers\CreditAccountsController@nonMemberAdvancesDownload'); //currently not using
    // Route::get('/credit-accounts/memberAdvances', 'App\Http\Controllers\CreditAccountsController@advances'); //using for member and non-member advances
    Route::get('/credit-accounts/viewActivityLogs/{type}/{id}', 'App\Http\Controllers\CreditAccountsController@viewActivityLogs');
    Route::post('/credit-accounts/add', 'App\Http\Controllers\CreditAccountsController@addAdvances');
    Route::get('/credit-accounts/add/{id}', 'App\Http\Controllers\CreditAccountsController@detailAdvances');
    Route::put('/credit-accounts/add/{id}', 'App\Http\Controllers\CreditAccountsController@editAdvances');

    Route::put('/credit-accounts/refundMoney/{id}', 'App\Http\Controllers\CreditAccountsController@refundMoney');
    Route::get('/credit-accounts/refundMoneyAmount/{id}', 'App\Http\Controllers\CreditAccountsController@refundMoneyAmount');

    /** INCOME & INCOME DETAILS APIs **/
    Route::get('/income-details/incomemember', 'App\Http\Controllers\IncomeDetailsController@incomemember');
    Route::get('/income-details/incomemember/download/{type}', 'App\Http\Controllers\IncomeDetailsController@incomeMemberDownload');
    Route::get('/income-details/incomepaymenttracker', 'App\Http\Controllers\IncomeDetailsController@incomepaymenttracker');
    Route::get('/income-details/paymentReceipt/{id}', 'App\Http\Controllers\IncomeDetailsController@paymentReceipt');
    Route::get('/income-details/tdspayableReport', 'App\Http\Controllers\ReportsController@tdspayableReport');
    Route::get('/income-details/tdspayableReport/download/{type}', 'App\Http\Controllers\ReportsController@downloadTdsPayableReport');
    Route::get('/income-details/tdsreceivableReport', 'App\Http\Controllers\ReportsController@tdsreceivableReport');
    Route::get('/income-details/tdsreceivableReport/download/{type}', 'App\Http\Controllers\ReportsController@downloadTdsReceivableReport');
    Route::put('/income-details/paymemberbill/{id}', 'App\Http\Controllers\IncomeDetailsController@paymemberbill');
    Route::get('/income-details/incomenonmember', 'App\Http\Controllers\IncomeDetailsController@incomenonmember');
    Route::get('/income-details/hugeincomenonmember', 'App\Http\Controllers\IncomeDetailsController@incomenonmember');
    Route::get('/income-details/memberInvoicelist/{id}', 'App\Http\Controllers\IncomeDetailsController@MemberInvoiceList');
    Route::get('/common-billing/memberInvoicelist/{id}', 'App\Http\Controllers\CommonBillingController@MemberCommonInvoiceList');
    Route::get('/common-billing/unpaidInvoices/{id}', 'App\Http\Controllers\CommonBillingController@MemberCommonInvoiceList');
    Route::get('/income-details/memberReceiptslist/{id}', 'App\Http\Controllers\IncomeDetailsController@MemberReceiptsList');
    Route::get('/income-details/previewWithGenerate/{unit_id}', 'App\Http\Controllers\IncomeDetailsController@previewWithGenerate');
    Route::get('/income-details/previewWithGenerate/bulk/download', 'App\Http\Controllers\IncomeDetailsController@previewWithGenerateBulkDownload');
    Route::get('/income-details/previewWithGenerate/{unit_id}/download', 'App\Http\Controllers\IncomeDetailsController@previewWithGenerateDownload')->where('unit_id', '^[0-9,]+$');
    Route::get('/income-details/previewWithGenerate/{unit_id}/{additional_data}', 'App\Http\Controllers\IncomeDetailsController@previewWithGenerate');
    Route::get('/income-details/getMembers', 'App\Http\Controllers\IncomeDetailsController@getmembers');
    Route::get('/income-details/getGeneralSettings', 'App\Http\Controllers\IncomeDetailsController@getGeneralSettings');
    Route::get('/income-details/getBankCashAccountDetail', 'App\Http\Controllers\IncomeDetailsController@getBankCashAccountDetail');
    Route::post('/income-details/createReceipt', 'App\Http\Controllers\IncomeDetailsController@createReceipt');
    Route::get('/income-details/getUnpaidInvoices/{id}', 'App\Http\Controllers\IncomeDetailsController@getUnpaidInvoices');
    //Route::get('/income-details/getMemberPaidInvoices', 'App\Http\Controllers\IncomeDetailsController@getMemberPaidInvoices');
    Route::post('/income-details/editMemberName/{unit_id}', 'App\Http\Controllers\IncomeDetailsController@EditMemberName');
    //Route::get('/income-details/getInvoiceSettings', 'App\Http\Controllers\IncomeDetailsController@getInvoiceSettings');
    //Route::get('/income-details/getMembersUnpaidInvoicesParticulars', 'App\Http\Controllers\IncomeDetailsController@getMembersUnpaidInvoicesParticulars');
    Route::get('/income-details/getMembersPreviousBills', 'App\Http\Controllers\IncomeDetailsController@getMembersPreviousBills');
    Route::delete('/income-details/confirmInvoiceCancellation/{id}', 'App\Http\Controllers\IncomeDetailsController@confirmInvoiceCancellation');
    Route::get('/income-details/getReceiptListLatest/{id}', 'App\Http\Controllers\IncomeDetailsController@getReceiptListLatest');
    Route::put('/income-details/incomemember/sendNotification/{invoice_number}/{id}', 'App\Http\Controllers\IncomeDetailsController@incomeMemberSendNotification');
    Route::put('/income-details/addoutstanding/{id}', 'App\Http\Controllers\IncomeDetailsController@addoutstanding');
    Route::post('/income-details/generateBulkbill', 'App\Http\Controllers\IncomeDetailsController@generateBulkbill');
    Route::put('/income-details/incomePaymentTrackerConfirmation/{status}/{id}', 'App\Http\Controllers\IncomeDetailsController@paymentTrackerConfirmation');
    route::get('/income-details/updatePaymentTracker/{id}', 'App\Http\Controllers\IncomeDetailsController@getUpdatePaymentTrackerData');
    Route::get('/income-details/fetchDataIncomePaymentTrackerConfirmationReversal/{id}', 'App\Http\Controllers\IncomeDetailsController@fetchDataIncomePaymentTrackerConfirmationReversal');
    Route::put('/income-details/updatePaymentTracker/{id}', 'App\Http\Controllers\IncomeDetailsController@updateIncomePaymentTracker');
    Route::get('/income-details/paymemberbill/{id}', 'App\Http\Controllers\IncomeDetailsController@fetchDataPayMemberBill');
    Route::get('/income-details/generatemanualbill/{id}', 'App\Http\Controllers\IncomeDetailsController@generatemanualbill');
    Route::get('/income-details/incomemember/view_applied_rules/{id}/{unit_id}', 'App\Http\Controllers\IncomeDetailsController@viewAppliedrules');

    // non-member master
    Route::get('/income-details/nonmemberMaster', 'App\Http\Controllers\DCOController@nonmembermaster');
    Route::get('/income-details/addnonmemberMaster/{id}', 'App\Http\Controllers\DCOController@nonmembermasterById');
    Route::post('/income-details/addnonmemberMaster', 'App\Http\Controllers\DCOController@addNonMemberMaster');
    Route::put('/income-details/addnonmemberMaster/{id}', 'App\Http\Controllers\DCOController@updateNonMemberMaster');
    Route::get('/income/getMembers', 'App\Http\Controllers\CommonController@incomegetmembers');
    Route::get('/income/getInvoices', 'App\Http\Controllers\CommonController@incomeGetInvoices'); // /{id}
    Route::get('/income/getInvoiceParticular', 'App\Http\Controllers\CommonController@incomeGetInvoiceParticular');
    // Route::get('/income/getMembers/{keyword}', 'App\Http\Controllers\CommonController@incomegetmembers');
    Route::get('/income-tracker-setting/readIncomeAccounts', 'App\Http\Controllers\IncomeTrackerSettingController@incomeaccountslist');
    Route::get('/income-tracker-setting/readIncomeAccounts/log', 'App\Http\Controllers\IncomeTrackerSettingController@incomeAccountsListLog');
    Route::post('/income-tracker-setting/addIncomeAccount', 'App\Http\Controllers\IncomeTrackerSettingController@addNonMemberAccount');
    // Route::put('/income-tracker-setting/updateNonMemberAccount/{id}', 'App\Http\Controllers\IncomeTrackerSettingController@updateNonMemberAccount');
    Route::post('/income-tracker-setting/updateNonMemberAccount', 'App\Http\Controllers\IncomeTrackerSettingController@updateNonMemberAccount');
    Route::match(['post', 'put'], '/income-tracker-setting/paymentreminder', 'App\Http\Controllers\IncomeTrackerSettingController@paymentReminder');
    Route::get('/income-tracker-setting/paymentreminder', 'App\Http\Controllers\IncomeTrackerSettingController@paymentReminder');
    Route::delete('/income-tracker-setting/paymentreminder', 'App\Http\Controllers\IncomeTrackerSettingController@paymentReminder');
    // Route::get('/income/getIncomeAccounts', 'App\Http\Controllers\CommonController@incomeaccountslist');
    Route::post('/income-details/addnonmemberincome', 'App\Http\Controllers\IncomeDetailsController@addNonMemberIncome');
    Route::get('/income-tracker-setting/readParticularSettings', 'App\Http\Controllers\IncomeTrackerSettingController@readParticularSettings');
    Route::get('/income-tracker-setting/readParticularSettings/log', 'App\Http\Controllers\IncomeTrackerSettingController@readParticularSettingsLog');
    Route::post('/income-tracker-setting/readParticularSettings', 'App\Http\Controllers\IncomeTrackerSettingController@updateParticularSettings');
    Route::get('/income-tracker-setting/readGeneralSettings', 'App\Http\Controllers\IncomeTrackerSettingController@readGeneralSettings');
    Route::post('/income-tracker-setting/readGeneralSettings', 'App\Http\Controllers\IncomeTrackerSettingController@addGeneralSettings');
    Route::delete('/income-details/cancelIncomeNonMemberInvoice', 'App\Http\Controllers\IncomeDetailsController@cancelIncomeNonMemberInvoice');
    Route::get('/income-details/pay_nonmember_bill/{id}', 'App\Http\Controllers\IncomeDetailsController@payNonMemberBill');
    Route::put('/income-details/pay_nonmember_bill/{id}', 'App\Http\Controllers\IncomeDetailsController@updatePayNonMemberBill');
    Route::post('/income-details/incomePaymentTrackerClear', 'App\Http\Controllers\IncomeDetailsController@incomePaymentTrackerClear');
    Route::put('/income-details/editMemberName/{type}/{id}', 'App\Http\Controllers\IncomeDetailsController@editMemberName');
    Route::patch('/income-details/stopInvoicing/{id}', 'App\Http\Controllers\IncomeDetailsController@stopInvoicing');

    /** TRANSACTIONS APIs **/
    Route::get('/transaction/listVoucherTracker', 'App\Http\Controllers\TransactionController@listVoucherTracker');
    Route::get('/transaction/viewVoucherReciept/{id}', 'App\Http\Controllers\TransactionController@viewVoucherReciept');
    Route::get('/transaction/viewVoucherReciept/download/{id}', 'App\Http\Controllers\TransactionController@viewVoucherReciept');
    // Route::get('/transaction/incorrectLedgerEentries', 'App\Http\Controllers\IncorrectLedgerTransaction@incorrectledgertransaction'); // old route
    Route::get('/transaction/incorrect_ledger_entries', 'App\Http\Controllers\TransactionController@incorrectledgertransaction'); // new route
    Route::get('/transaction/listTransactionMonthly/{id}/{month?}/{year?}', 'App\Http\Controllers\TransactionController@listTransaction');
    Route::get('/transaction/listTransactionMonthly/download/excel/{id}/{month?}/{year?}', 'App\Http\Controllers\TransactionController@downloadTransaction');
    Route::get('/transaction/listTransactionYearly/{id}', 'App\Http\Controllers\TransactionController@listTransaction');
    Route::get('/transaction/editTransaction/{id}/{ledger_id?}/{month?}/{year?}', 'App\Http\Controllers\TransactionController@listTransactionDetail');
    Route::post('/transaction/addDebitEntry', 'App\Http\Controllers\TransactionController@addDebitNote');
    Route::post('/transaction/addMultiplePaymentVoucher', 'App\Http\Controllers\TransactionController@addMultiplePaymentVoucher');
    Route::post('/transaction/addPaymentVoucher', 'App\Http\Controllers\TransactionController@addPaymentVoucher');
    Route::post('/transaction/addContraEntry', 'App\Http\Controllers\TransactionController@addContraEntry');
    Route::post('/transaction/addJournalEntry', 'App\Http\Controllers\TransactionController@addJournalEntry');
    Route::post('/transaction/addTopupInterestTransaction', 'App\Http\Controllers\TransactionController@addTopupInterestTransaction');
    Route::put('/transaction/addTopupInterestTransaction/{type}/{id}', 'App\Http\Controllers\TransactionController@addTopupInterestTransaction');
    Route::delete('/transaction/deleteVoucher/{id}', 'App\Http\Controllers\TransactionController@deleteVoucher');
    Route::delete('/transaction/delete/{id}/{ledger_id?}/{month?}/{year?}', 'App\Http\Controllers\TransactionController@delete');
    Route::put('/transaction/editTransaction/{id}/{ledger_id?}/{month?}/{year?}', 'App\Http\Controllers\TransactionController@editTransaction');

    /** ACCOUNTS APIs **/
    Route::get('/accounts/viewLedgers', 'App\Http\Controllers\AccountsController@getLedgerTree');

    Route::get('/accountsetting/accountset', 'App\Http\Controllers\AccountsController@accountset');
    Route::post('/accountsetting/accountset', 'App\Http\Controllers\AccountsController@closeAccounts');
    Route::get('/accountsreporting/trial_balance', 'App\Http\Controllers\AccountsReportingController@trialBalance');
    Route::get('/accountsreporting/trial_balance/download/{method}', 'App\Http\Controllers\AccountsReportingController@downloadTrialBalance');
    Route::get('/accounts/getAllLedgers', 'App\Http\Controllers\AccountsController@getAllLedgers');
    Route::get('/tax/viewTax', 'App\Http\Controllers\TaxController@taxClasses');
    Route::get('/tax/viewTaxRule/{id}', 'App\Http\Controllers\TaxController@viewTaxRule');
    Route::get('/tax/viewTaxRuleInfo/{id}', 'App\Http\Controllers\TaxController@viewTaxRule');
    Route::get('/accountsetting/financialYearList', 'App\Http\Controllers\AccountsController@financialYearList');

    Route::get('/taxexemption/viewTaxExemption', 'App\Http\Controllers\TaxExemptionController@viewTaxExemption');
    Route::post('/taxexemption/addtaxexemption', 'App\Http\Controllers\TaxExemptionController@addtaxexemption');
    Route::get('/taxexemption/getTaxExemption/{id}', 'App\Http\Controllers\TaxExemptionController@getTaxExemption');
    Route::put('/taxexemption/editTaxExemption/{id}', 'App\Http\Controllers\TaxExemptionController@editTaxExemption');

    Route::get('/tds-challans/list', 'App\Http\Controllers\TDSChallansController@tdsList');
    Route::get('/tds-challans/view/{id}', 'App\Http\Controllers\TDSChallansController@getTdsDetails');
    Route::post('/tds-challans/add', 'App\Http\Controllers\TDSChallansController@addTdsChallans');
    Route::delete('/tds-challans/cancel-challans/{id}', 'App\Http\Controllers\TDSChallansController@cancelChallans');
    Route::get('/accounts/bankReconciliation', 'App\Http\Controllers\AccountsController@bankReconciliation');
    Route::get('/accounts/bankReconciliationForm', 'App\Http\Controllers\AccountsController@bankReconciliationForm');
    Route::get('/accounts/bankReconciliationFormTable', 'App\Http\Controllers\AccountsController@bankReconciliationFormTable');
    Route::get('/accounts/viewBankAccounts', 'App\Http\Controllers\AccountsController@viewBankAccounts');
    Route::get('/accounts/viewBankAccounts/download/{type}', 'App\Http\Controllers\AccountsController@downloadBankAccounts');
    Route::post('/accounts/fetchCurrentYearDetail', 'App\Http\Controllers\AccountsController@fetchCurrentYearDetail');
    Route::get('/accountsreporting/membersUnitAccountStatementReport', 'App\Http\Controllers\AccountsReportingController@MemberUnitLedgerStatement');
    Route::get('/accountsreporting/membersUnitAccountStatementReport/download/{type}', 'App\Http\Controllers\AccountsReportingController@downloadMemberUnitLedgerStatement');
    Route::post('/accounts/groupListing', 'App\Http\Controllers\AccountsController@viewGroups');
    Route::put('/accounts/deleteLedger/{id}', 'App\Http\Controllers\AccountsController@disableLedger');
    Route::get('/accounts/bankReconciliationTable', 'App\Http\Controllers\AccountsController@bankReconciliationLastTable');
    Route::put('/accounts/bankReconciliation', 'App\Http\Controllers\AccountsController@confirmBankReconciliation');
    Route::patch('/accounts/bankRecoSaveReferenceNumber', 'App\Http\Controllers\AccountsController@bankRecoSaveReferenceNumber');
    Route::patch('/accounts/bankRecoSaveBankDate', 'App\Http\Controllers\AccountsController@bankRecoSaveBankDate');
    Route::delete('/accounts/bankRecoRemoveBankDate', 'App\Http\Controllers\AccountsController@bankRecoRemoveBankDate');

    Route::post('/accounts/postNewLedger', 'App\Http\Controllers\AccountsController@postNewLedger');
    Route::post('/accounts/createLedger', 'App\Http\Controllers\AccountsController@createLedger');
    Route::get('/accounts/editLedger/{id}', 'App\Http\Controllers\AccountsController@editLedgerget'); // not in used right now because /accounts/getLedgerById/{id} is used 
    Route::put('/accounts/editLedger/{id}', 'App\Http\Controllers\AccountsController@editLedger');
    //Route::get('/accounts/viewLedger/{id}', 'App\Http\Controllers\AccountsController@viewLedger');
    Route::get('/accounts/getLedgerById/{id}', 'App\Http\Controllers\AccountsController@getLedgerById');
    //Route::post('/accounts/updateLedger', 'App\Http\Controllers\AccountsController@updateLedger');

    Route::get('/accounts/viewGroups', 'App\Http\Controllers\AccountsController@viewGroups');
    Route::get('/accounts/getGroupById/{id}', 'App\Http\Controllers\AccountsController@getGroupById');
    Route::post('/accounts/postNewGroup', 'App\Http\Controllers\AccountsController@postNewGroup');
    Route::put('/accounts/editGroup/{id}', 'App\Http\Controllers\AccountsController@editGroup');
    Route::put('/accounts/changeGroupStatus/{id}', 'App\Http\Controllers\AccountsController@changeGroupStatus');

    Route::get('/accounts/editCashAccount/{id}', 'App\Http\Controllers\AccountsController@fetchDataEditCashAccount');
    Route::put('/accounts/editCashAccount/{id}', 'App\Http\Controllers\AccountsController@editCashAccount');
    Route::post('/accounts/addBankAccount', 'App\Http\Controllers\AccountsController@addBankAccount');
    Route::put('/accounts/editBankAccount/{id}', 'App\Http\Controllers\AccountsController@editBankAccount');
    Route::get('/accounts/editBankAccount/{id}', 'App\Http\Controllers\AccountsController@getBankAccount');
    Route::get('/accounts/viewUsers/{role_name}/{id}', 'App\Http\Controllers\UserDetailsController@listSocRoleMembers');
    Route::post('/accounts/addBankCashTransaction/{type}', 'App\Http\Controllers\AccountsController@addBankCashTransaction');
    Route::post('/accounts/depositCash', 'App\Http\Controllers\AccountsController@addBankCashTransaction');
    //Route::post('/accounts/createLedgerBal', 'App\Http\Controllers\AccountsController@cashLedgerBal');
    //Route::post('/accounts/getTransacttionList', 'App\Http\Controllers\AccountsController@cashLedgerBal');
    Route::post('/accounts/addTransactionEntry', 'App\Http\Controllers\AccountsController@addBankCashTransaction');
    Route::get('/accounts/getBankAccounts', 'App\Http\Controllers\AccountsController@getBankAccounts');
    Route::get('/accounts/getCashAccounts', 'App\Http\Controllers\AccountsController@getCashAccounts');
    Route::get('/accounts/viewCashAccounts', 'App\Http\Controllers\AccountsController@viewCashAccounts');

    Route::get('/accounts/getInvestMentById/{id}', 'App\Http\Controllers\AccountsController@getInvestmentById');
    Route::post('/accounts/addInvestment', 'App\Http\Controllers\AccountsController@addInvestMent');
    Route::put('/accounts/editInvestMent/{id}', 'App\Http\Controllers\AccountsController@editInvestMent');

    Route::post('/accounts/addTax', 'App\Http\Controllers\AccountsController@addTax');
    Route::put('/accounts/editTax/{id}', 'App\Http\Controllers\AccountsController@editTax');
    Route::get('/accounts/editTax/{id}', 'App\Http\Controllers\AccountsController@getTaxDetails');
    //Route::post('/accounts/saveTaxDetails', 'App\Http\Controllers\AccountsController@saveTaxDetails');
    //Route::post('/accounts/updateTaxDetails', 'App\Http\Controllers\AccountsController@saveTaxDetails');

    Route::post('/accounts/checkDuplication', 'App\Http\Controllers\AccountsController@checkDuplication');
    Route::get('/accounts/viewTaxRule/{id}', 'App\Http\Controllers\TaxController@viewTaxRule');
    Route::get('/accounts/getTaxList', 'App\Http\Controllers\AccountsController@getTaxList');
    Route::get('/accounts/getCurrentTaxClasses', 'App\Http\Controllers\AccountsController@getCurrentTaxClasses');
    Route::get('/accounts/getAllTaxRuleClasses', 'App\Http\Controllers\AccountsController@getAllTaxRuleClasses');
    Route::get('/accounts/getTaxCategoriesDiffById', 'App\Http\Controllers\AccountsController@getTaxCategoriesDiffById');

    Route::post('/accounts/checkGroupNameDuplication', 'App\Http\Controllers\AccountsController@checkGroupNameDuplication');
    Route::post('/accounts/addtaxexemption', 'App\Http\Controllers\AccountsController@addtaxexemption');
    Route::put('/accounts/edittaxexemption/{id}', 'App\Http\Controllers\AccountsController@edittaxexemption');
    Route::get("/accountsreporting/balancesheet", "App\Http\Controllers\AccountsReportingController@balanceSheet");
    Route::post("/accountsreporting/balancesheet/download/{method}", "App\Http\Controllers\AccountsReportingController@downloadBalanceSheet");
    Route::get("/accountsreporting/cashflow", "App\Http\Controllers\AccountsReportingController@cashFlow");
    Route::get("/accountsreporting/cashflow/download/{method}", "App\Http\Controllers\AccountsReportingController@downloadCashFlow");
    Route::get("/accountsreporting/profit_and_lossT", "App\Http\Controllers\AccountsReportingController@profitAndLoss");
    Route::post("/accountsreporting/profit_and_lossT/download/{method}", "App\Http\Controllers\AccountsReportingController@downloadProfitAndLoss");

    Route::post('/accounts/changeTaxStatus', 'App\Http\Controllers\AccountsController@changeTaxStatus');
    //Route::post('/accounts/deleteTaxCategories', 'App\Http\Controllers\AccountsController@deleteTaxCategories');
    Route::post('/accounts/createBulkAllottees', 'App\Http\Controllers\MemberController@createAllottees');
    //Route::post('/accounts/addLedgerTransaction', 'App\Http\Controllers\AccountsController@addLedgerTransaction');
    Route::get('/assets/investmentslist', 'App\Http\Controllers\AccountsController@investmentsList');
    Route::get('/assets/assetsList', 'App\Http\Controllers\AccountsController@assetsList');
    Route::get('/assets/assetDetails/{id}', 'App\Http\Controllers\AccountsController@assetDetails');
    Route::get('/assets/settings', 'App\Http\Controllers\AccountsController@assetSettings');
    Route::get('/assets/assetDetails/download/{type}', 'App\Http\Controllers\AccountsController@assetsDownload');
    Route::post('/assets/addAsset', 'App\Http\Controllers\AccountsController@addAsset');
    Route::put('/assets/editAsset/{id}', 'App\Http\Controllers\AccountsController@editAsset');
    Route::get('/assets/editAsset/{id}', 'App\Http\Controllers\AccountsController@fetchEditAssetData');
    Route::get('/accounts/getCurrentYearDetail', 'App\Http\Controllers\AccountsController@getCurrentYearDetail');
    Route::post('/assets/settings', 'App\Http\Controllers\AccountsController@addAssetSettings');
    Route::put('/assets/settings', 'App\Http\Controllers\AccountsController@updateAssetSettings');
    Route::patch('/assets/settings/{id}', 'App\Http\Controllers\AccountsController@updateAssetSetting');
    Route::delete('/assets/settings/{id}', 'App\Http\Controllers\AccountsController@deleteAssetSettings');

    /** USERS AND ROLES APIs **/
    Route::get('/users/list', 'App\Http\Controllers\UserDetailsController@userlist');
    Route::get('/users/userDetails/{id}/{role}', 'App\Http\Controllers\UserDetailsController@userDetails');
    Route::get('/users/roles/{id}', 'App\Http\Controllers\UserDetailsController@userRoles');
    Route::put('/users/roles/{id}', 'App\Http\Controllers\UserDetailsController@assignUserRoles');
    Route::get('/user-details/usersroles', 'App\Http\Controllers\UserDetailsController@usersroles');
    Route::get('/roles/listSocRoles', 'App\Http\Controllers\UserDetailsController@listSocRoles');
    Route::get('/roles/add/{id}', 'App\Http\Controllers\UserDetailsController@roleDetails');
    Route::post('/roles/add', 'App\Http\Controllers\UserDetailsController@usersrolesadd');
    Route::put('/roles/add/{id}', 'App\Http\Controllers\UserDetailsController@usersrolesupdate');
    Route::get('/roles/viewUsers/{role_name}/{id}', 'App\Http\Controllers\UserDetailsController@listSocRoleMembers');
    Route::get('/staffs/staffLists', 'App\Http\Controllers\StaffDetailsController@stafflist');
    Route::post('/staffs/staffLists/downloadStaffs/{type}', 'App\Http\Controllers\StaffDetailsController@downloadStaff');
    Route::get('/staffs/staffDetails/{id}', 'App\Http\Controllers\StaffDetailsController@staffDetails');
    Route::get('/staffs/staffDetailsByMobile', 'App\Http\Controllers\StaffDetailsController@staffDetailsByMobile');
    Route::post('/staffs/addStaff', 'App\Http\Controllers\StaffDetailsController@addstafflist');
    Route::put('/staffs/editStaff/{id}', 'App\Http\Controllers\StaffDetailsController@editstafflist');
    Route::put('/staffs/updateStatus/{id}', 'App\Http\Controllers\StaffDetailsController@updateStatus');
    Route::post('/staffs/sendSms', 'App\Http\Controllers\StaffDetailsController@sendSms');
    Route::post('/staffs/verify', 'App\Http\Controllers\StaffDetailsController@verifyStaff');
    Route::post('/roles/viewUsers/{role_name}/{id}/download/{type}', 'App\Http\Controllers\UserDetailsController@dowloadListSocRoleMembers');

    Route::get('/staffs/settings', 'App\Http\Controllers\StaffDetailsController@staffcategorylist');
    Route::get('/staffs/staffCategory', 'App\Http\Controllers\StaffDetailsController@getStaffCategoryList'); // create staff category list api with full data for dropdown in add/edit staff form
    // Route::post('/staffs/settings/saveStaffCat', 'App\Http\Controllers\StaffDetailsController@savestaffcategorylist');   // old route
    Route::post('/staffs/settings', 'App\Http\Controllers\StaffDetailsController@savestaffcategorylist'); // new route as per old ui
    Route::patch('/staffs/settings/{id}', 'App\Http\Controllers\StaffDetailsController@updatestaffcategorylist');
    Route::patch('/staffs/settings/updateCategoryStatus/{id}', 'App\Http\Controllers\StaffDetailsController@updateCategoryStatus');

    /** SOCIETIES APIs **/
    Route::get('/societies/emailtemplates/{id?}', 'App\Http\Controllers\GeneralSettingsController@autoresponsemaillist');
    Route::get('/societies/smstemplates/{id?}', 'App\Http\Controllers\GeneralSettingsController@autoresponsesmslist');
    Route::get('/societies/nocForms', 'App\Http\Controllers\CommonController@noctemplates');
    Route::get('/societies/emailTemplateDetail/{id}', 'App\Http\Controllers\GeneralSettingsController@emailTemplateDetail');
    Route::put('/societies/emailtemplateedit/{id}', 'App\Http\Controllers\GeneralSettingsController@emailTemplateEdit');
    Route::patch('/societies/emailtemplates/{id}', 'App\Http\Controllers\GeneralSettingsController@emailTemplateDisable');
    Route::patch('/societies/smstemplates/{id}', 'App\Http\Controllers\GeneralSettingsController@smsTemplateDisable');
    Route::post('/societies/emailSmsStatus', 'App\Http\Controllers\GeneralSettingsController@emailSMSStatusChange');
    Route::post('/societies/prefeencesr', 'App\Http\Controllers\GeneralSettingsController@setAppPreferences');
    Route::get('/societies/preferences', 'App\Http\Controllers\GeneralSettingsController@getAppPreferences');
    Route::put('/societies/preferences', 'App\Http\Controllers\GeneralSettingsController@setAppPreferences');

    /** UNITS & BUILDING APIs **/
    Route::get('/units/list', 'App\Http\Controllers\GeneralSettingsController@complexUnitList');
    Route::get('/units/getUnitCategories', 'App\Http\Controllers\CommonController@unitcategories');
    Route::get('/building/list', 'App\Http\Controllers\BuildingDetailsController@list');
    Route::get('/units/getUnitTypes', 'App\Http\Controllers\CommonController@unittypes');
    Route::post('/building/add', 'App\Http\Controllers\BuildingController@addBuilding');
    Route::put('/building/edit/{id}', 'App\Http\Controllers\BuildingController@addBuilding');
    Route::post('/units/bulkUnitAdd', 'App\Http\Controllers\UnitsController@bulkUnitAdd');
    Route::delete('/units/delete/{id}', 'App\Http\Controllers\UnitsController@deleteUnits');
    Route::delete('/building/delete/{id}', 'App\Http\Controllers\BuildingController@deleteBuilding');
    Route::get('/gateSettings/list', 'App\Http\Controllers\GatesSettingsController@gateSettingsList');
    Route::get('/gates/list', 'App\Http\Controllers\GatesSettingsController@gatesList');
    Route::put('/gateSettings/edit', 'App\Http\Controllers\GatesSettingsController@editGateSettings');
    Route::get('/building/deletedBuildingList', 'App\Http\Controllers\BuildingDetailsController@deletedBuildingList');
    Route::post('/building/downloadBuilding/{type}', 'App\Http\Controllers\BuildingDetailsController@downloadBuildingList');
    Route::post('building/downloadDeletedBuilding/{type}', 'App\Http\Controllers\BuildingDetailsController@downloadDeletedBuildingList');
    Route::get('/building/edit/{id}', 'App\Http\Controllers\BuildingDetailsController@getBuildingDetails');
    Route::put('/societies/editUnitType/{id}', 'App\Http\Controllers\GeneralSettingsController@editUnitType');
    Route::get('/societies/unitTypeDetails/{id}', 'App\Http\Controllers\GeneralSettingsController@unitTypeDetails');

    // Unit Category APIs
    Route::get('/societies/listUnitType', 'App\Http\Controllers\GeneralSettingsController@listUnitType');
    Route::post('/societies/defineUnitType', 'App\Http\Controllers\GeneralSettingsController@defineUnitType');
    Route::post('/societies/downloadSocUnits/{type}', 'App\Http\Controllers\GeneralSettingsController@downloadSocUnits');
    Route::get('/societies/listUnitTypeDropDown', 'App\Http\Controllers\GeneralSettingsController@listUnitTypeDropDown');

    // complex unit APIs
    Route::post('/units/downloadSocUnitList/{type}', 'App\Http\Controllers\UnitsController@downloadSocUnitList');
    Route::post('/units/add', 'App\Http\Controllers\UnitsController@addUnit');
    Route::get('/units/edit/{id}', 'App\Http\Controllers\UnitsController@getUnitDetail');
    Route::put('/units/generateVPA/{id}', 'App\Http\Controllers\UnitsController@generateVPA');
    Route::put('/units/edit/{id}', 'App\Http\Controllers\UnitsController@editUnitDetails');
    Route::get('/units/fetchLatePaymentChargesList', 'App\Http\Controllers\UnitsController@fetchLatePaymentChargesList');
    Route::get('/units/fetchUnitLatePaymentCharges', 'App\Http\Controllers\UnitsController@fetchUnitLatePaymentCharges');

    // Auto invoicing rule APIs
    Route::get('/income-tracker-invoice-setting/invoicelisting', 'App\Http\Controllers\AutoInvoiceRuleSettingController@invoiceListing');
    Route::post('/income-tracker-invoice-setting/add_rule', 'App\Http\Controllers\AutoInvoiceRuleSettingController@addRule');
    Route::get('/income-tracker-invoice-setting/add_rule/{id}', 'App\Http\Controllers\AutoInvoiceRuleSettingController@getRuleById');
    Route::get('/income-tracker-invoice-setting/preview_income/{id}', 'App\Http\Controllers\AutoInvoiceRuleSettingController@previewIncomeRule');
    Route::put('/income-tracker-invoice-setting/add_rule/{id}', 'App\Http\Controllers\AutoInvoiceRuleSettingController@updateRule');
    Route::get('/income-tracker-invoice-setting/unitrules/{id}', 'App\Http\Controllers\AutoInvoiceRuleSettingController@unitRules');
    Route::post('/income-tracker-invoice-setting/add_late_payment_charge_rule', 'App\Http\Controllers\AutoInvoiceRuleSettingController@addLatePaymentChargeRule');
    Route::get('/income-tracker-invoice-setting/late_payment_charge_rule', 'App\Http\Controllers\AutoInvoiceRuleSettingController@latePaymentChargeRule');
    Route::get('/income-tracker-invoice-setting/late_payment_charge_rule/{id}', 'App\Http\Controllers\AutoInvoiceRuleSettingController@getLatePaymentChargeRuleById');
    Route::put('/income-tracker-invoice-setting/late_payment_charge_rule/{id}', 'App\Http\Controllers\AutoInvoiceRuleSettingController@updateLatePaymentChargeRule');
    Route::get('/income-tracker-invoice-setting/get_unit_type', 'App\Http\Controllers\AutoInvoiceRuleSettingController@unitTypelist');
    Route::get('/income-tracker-invoice-setting/member_income_account', 'App\Http\Controllers\AutoInvoiceRuleSettingController@memberIncomeAccount');

    // Incidental Invoicing Rules
    Route::get('/income-tracker-setting/readcommonbilling/activebilling', 'App\Http\Controllers\IncomeTrackerSettingController@readcommonbilling');
    Route::get('/income-tracker-setting/rule/{id}', 'App\Http\Controllers\IncomeTrackerSettingController@ruleById');
    Route::post('/income-tracker-setting/rule', 'App\Http\Controllers\IncomeTrackerSettingController@createIncidentalRule');
    Route::put('/income-tracker-setting/rule/{id}', 'App\Http\Controllers\IncomeTrackerSettingController@updateIncidentalRule');
    Route::post('/income-tracker-setting/latechargesrule', 'App\Http\Controllers\IncomeTrackerSettingController@latechargesrule');

    /** EXPENSES APIs **/
    Route::get('/otherexpense/expenses', 'App\Http\Controllers\OtherexpenseController@MiscellaneousList');
    Route::post('/otherexpense/expenses/download/{type}', 'App\Http\Controllers\OtherexpenseController@downloadMiscellaneousList');
    Route::get('/otherexpense/expenseDetails/{id}', 'App\Http\Controllers\OtherexpenseController@ExpenseDetails');
    Route::post('/expensetracker/settingsApply', 'App\Http\Controllers\ExpenseSetupController@expensesettingsadd');
    Route::get('/expensetracker/settingsApply', 'App\Http\Controllers\ExpenseSetupController@expensesettingslist');

    Route::get('/expensetracker/expensetaxsetup', 'App\Http\Controllers\ExpenseSetupController@expenseTaxSetup');
    Route::post('/expensetracker/expensetaxsetup', 'App\Http\Controllers\ExpenseSetupController@addExpenseGST');

    Route::get('/expensetracker/expensetdsrate/{id?}', 'App\Http\Controllers\ExpenseSetupController@expensetdsrate');
    Route::delete('/expensetracker/deleteexpensetdsrate/{id}', 'App\Http\Controllers\ExpenseSetupController@expensetdsrate');
    Route::post('/expensetracker/addexpensetdsrate', 'App\Http\Controllers\ExpenseSetupController@addExpenseTdsRate');
    Route::put('/expensetracker/addexpensetdsrate/{id}', 'App\Http\Controllers\ExpenseSetupController@addExpenseTdsRate');

    Route::get('/expensetracker/setExpenseRange', 'App\Http\Controllers\ExpenseSetupController@expenseApproval');
    Route::post('/expensetracker/setExpenseRange', 'App\Http\Controllers\ExpenseSetupController@expenseApprovalAdd');
    Route::delete('/expensetracker/delExpenseRange/{id}', 'App\Http\Controllers\ExpenseSetupController@expenseApprovalDelete');

    Route::post('/expensetracker/expenseaccountsbudgets', 'App\Http\Controllers\ExpenseSetupController@addExpenseBudget');
    Route::get('/expensetracker/expenseaccountsbudgets', 'App\Http\Controllers\ExpenseSetupController@listExpenseBudget');
    Route::get('/expensetracker/expenseTaxSetupDropDown', 'App\Http\Controllers\ExpenseSetupController@expenseTaxSetupDD');
    // Route::get('/expensetracker/expenseaccountsbudgets', 'App\Http\Controllers\ExpenseSetupController@getDataExpenseAccountsBudgets');
    Route::post('/otherexpense/addExpense', 'App\Http\Controllers\OtherexpenseController@addNewExpense');
    Route::post('/vendorbill/addVendorAdvances', 'App\Http\Controllers\OtherexpenseController@addVendorAdvances');
    Route::get('/vendorbill/addVendorAdvances/{id}', 'App\Http\Controllers\OtherexpenseController@vendorAdvancesDetails');
    Route::put('/vendorbill/addVendorAdvances/{id}', 'App\Http\Controllers\OtherexpenseController@vendorAdvancesEdit');

    /** PARKING AND MANAGEMENT COMMITTEE APIs **/
    Route::get('/parking-allotments/list', 'App\Http\Controllers\DCOController@parkingAllotmentList');
    Route::get('/parkingUnits/list', 'App\Http\Controllers\DCOController@parkingUnitsList');
    Route::get('/parking-allotments/add/{id}', 'App\Http\Controllers\DCOController@allocateParkingById');
    Route::post('/parking-allotments/add', 'App\Http\Controllers\DCOController@allocateParking');
    Route::put('/parking-allotments/add/{id}', 'App\Http\Controllers\DCOController@updateAllocateParking');
    Route::delete('/parking-allotments/delete/{id}', 'App\Http\Controllers\DCOController@deleteAllocateParking');
    Route::post('/parking-allotments/downloadParkingAllotments/{type}', 'App\Http\Controllers\DCOController@downloadParkingAllotments');
    Route::get('/committees/list', 'App\Http\Controllers\DCOController@committeeList');
    Route::post('/committees/add', 'App\Http\Controllers\DCOController@createCommittee');
    Route::get('/committees/panel/{id}', 'App\Http\Controllers\DCOController@committeeDetails');
    Route::get('/committees/panel/officeBearer/{id}', 'App\Http\Controllers\DCOController@committeeDetailsOfficeBearer');
    Route::get('/committees/panel/committeeMembers/{id}', 'App\Http\Controllers\DCOController@committeeDetailsCommitteeMembers');
    Route::get('/committees/getCommitteeMembers', 'App\Http\Controllers\CommonController@committeemembers');
    Route::delete('/committees/dissolve/{id}', 'App\Http\Controllers\DCOController@dissolveCommittee');
    Route::get('/parking/list', 'App\Http\Controllers\DCOController@parkingList');
    Route::get('/parking/registervehicle/{id}', 'App\Http\Controllers\DCOController@vehicleRegisterById');
    Route::post('/parking/downloadparking/{type}', 'App\Http\Controllers\DCOController@downloadVehicleRegistration');
    Route::post('/parking/registervehicle', 'App\Http\Controllers\DCOController@vehicleRegister');
    Route::get('/parking/registervehicle/card/{id}', 'App\Http\Controllers\DCOController@vehicleRegisterCardById');
    Route::get('/parking/registervehicle/memberList/{id}', 'App\Http\Controllers\DCOController@vehicleRegisterMemberListById');
    Route::get('/parking/registervehicle/allotmentList/{id}', 'App\Http\Controllers\DCOController@vehicleRegisterAllotmentListById');
    Route::patch('/parking/registervehicle/{id}/{detail_id}', 'App\Http\Controllers\DCOController@updateVehicleRegisterById'); // replace put -> patch
    Route::delete('/parking/registervehicle/{id}', 'App\Http\Controllers\DCOController@deleteVehicleRegisterById');

    Route::get('/noc/list', 'App\Http\Controllers\DCOController@noc_forms');
    Route::put('/noc/add_noc/{id}', 'App\Http\Controllers\DCOController@update_noc');
    Route::post('/noc/add_noc', 'App\Http\Controllers\DCOController@add_noc');
    Route::get('/noc/add_noc/{id}', 'App\Http\Controllers\DCOController@get_noc_by_id');
    Route::get('/noc/downloadNoc/{id}', 'App\Http\Controllers\DCOController@downloadNoc');
    Route::put('/noc/approveOrRejectNoc/{id}/{status}', 'App\Http\Controllers\DCOController@approveOrRejectNoc');
    Route::get('/notices/list', 'App\Http\Controllers\DCOController@noticesCirculars');
    Route::get('/notices/view/{id}', 'App\Http\Controllers\DCOController@noticesView');
    Route::post('notices/add_notice', 'App\Http\Controllers\DCOController@addNotice');
    // Route::get('/notices/viewResponses', 'App\Http\Controllers\DCOController@notices_survery_responses');
    Route::get('/notices/list_template', 'App\Http\Controllers\DCOController@noticesCircularsTemplates');
    Route::get('/notices/templates/{id}', 'App\Http\Controllers\DCOController@noticesCircularsTemplatesByID');
    Route::post('/notices/templates', 'App\Http\Controllers\DCOController@addNoticeTemplate');
    Route::put('/notices/templates/{id}', 'App\Http\Controllers\DCOController@updateNoticeTemplate');
    Route::delete('/notices/templates/{id}', 'App\Http\Controllers\DCOController@deleteNoticeTemplate');
    Route::post('/notices/downloadNotices/{id}', 'App\Http\Controllers\DCOController@downloadNotices');

    // Inventory Category APIs
    Route::get('/inventory/settings', 'App\Http\Controllers\InventoryController@categoryList');
    Route::post('/inventory/settings', 'App\Http\Controllers\InventoryController@addCategory');
    Route::put('/inventory/settings', 'App\Http\Controllers\InventoryController@updateCategory');
    Route::delete('/inventory/settings/{id}', 'App\Http\Controllers\InventoryController@deleteCategory');

    // Inventory Item APIs
    Route::get('/inventory/listInventory', 'App\Http\Controllers\InventoryController@itemList');
    Route::get('/inventory/listInventory/{id}', 'App\Http\Controllers\InventoryController@itemList');
    Route::post('/inventory/addInventoryItem', 'App\Http\Controllers\InventoryController@addInventoryItem');
    Route::post('/inventory/downloadInventory/{type}', 'App\Http\Controllers\InventoryController@downloadInventory');
    Route::put('/inventory/inward/{id}', 'App\Http\Controllers\InventoryController@inwardInventory');
    Route::post('/inventory/inward/download/{id}/{type}', 'App\Http\Controllers\InventoryController@downloadInwardInventory');
    Route::put('/inventory/outward/{id}', 'App\Http\Controllers\InventoryController@outwardInventory');
    Route::get('/inventory/listInventory/transaction/{id}', 'App\Http\Controllers\InventoryController@inventoryTransaction');

    Route::get('/common-billing/listcommonbill', 'App\Http\Controllers\CommonBillingController@listcommonbill');
    // Route::get('/common-billing/commonbilldetails/{id}', 'App\Http\Controllers\CommonBillingController@commonbilldetails');  change route to below route
    Route::get('/common-billing/downloadInvoice/{id}/{invoice_number}', 'App\Http\Controllers\CommonBillingController@commonbilldetails');
    Route::get('/income-details/downloadInvoice/{id}/{invoice_number}/viewInvoice', 'App\Http\Controllers\IncomeDetailsController@incomeDetails');
    Route::get('/income-details/downloadNonMemberInvoice/{id}/{invoice_number}/viewInvoice', 'App\Http\Controllers\IncomeDetailsController@incomeNonMemberDetails');

    // Route::post('/common-billing/addcommonbill', 'App\Http\Controllers\CommonBillingController@addcommonbill');
    Route::post('/common-billing/addcommonbill', 'App\Http\Controllers\CommonBillingController@postAddCommonBill');
    Route::put('/common-billing/addcommonbill/{id}', 'App\Http\Controllers\CommonBillingController@addcommonbill');
    Route::get('/common-billing/addcommonbill/{id}', 'App\Http\Controllers\CommonBillingController@dataForAddCommonBill');
    Route::get('/common-billing/listcommonbill/DownloadCommonbill/{type}', 'App\Http\Controllers\CommonBillingController@downloadCommonbill');
    Route::get('/common-billing/incidental/getLastReceipt/{id}', 'App\Http\Controllers\CommonBillingController@getLastReceipt');
    Route::get('/common-billing/getRateByParticularId', 'App\Http\Controllers\CommonController@incidentbillsparticulars');

    Route::get('/common-billing/paycommonbill/{id}', 'App\Http\Controllers\CommonBillingController@dataForPayCommonBill'); // need to check pass get_latest_name key or not
    Route::put('/common-billing/paycommonbill/{id}', 'App\Http\Controllers\CommonBillingController@payCommonBill'); // need to check pass get_latest_name key or not
    Route::post('/common-billing/paycommonbill', 'App\Http\Controllers\CommonBillingController@postPayCommonBill');
    Route::put('/common-billing/sendNotification/{invoice_number}/{id}', 'App\Http\Controllers\CommonBillingController@commonBillSendNotification');
    Route::delete('/common-billing/cancelcommonbill/{id}', 'App\Http\Controllers\CommonBillingController@cancelCommonBill');
    Route::get('/common-billing/downloadInvoice/{id}/{invoice_number}/download/{type}', 'App\Http\Controllers\CommonBillingController@commonbilldetailsDownload');
    Route::get('/income-details/downloadOriginalInvoice', 'App\Http\Controllers\IncomeDetailsController@downloadOriginalInvoice');
    Route::get('/income-details/downloadDuplicateInvoice', 'App\Http\Controllers\IncomeDetailsController@downloadOriginalInvoice');

    //Expense Tracker Purchase Form
    Route::get('/purchaseform/list', 'App\Http\Controllers\PurchaseFormController@PurchaseFormList');
    Route::get('/purchaseform/view/{purchase_form_id}', 'App\Http\Controllers\PurchaseFormController@purchaseFormDetail');
    Route::get('/purchaseform/view/{purchase_form_id}/download/{type}', 'App\Http\Controllers\PurchaseFormController@purchaseFormDetailDownload');
    Route::get('/purchaseform/view/{purchase_form_id}/items', 'App\Http\Controllers\PurchaseFormController@purchaseFormItems');
    Route::post('/purchaseform/add', 'App\Http\Controllers\PurchaseFormController@purchaseFormAdd');
    Route::put('/purchaseform/approve', 'App\Http\Controllers\PurchaseFormController@purchaseFormApprove');
    Route::put('/purchaseform/reject', 'App\Http\Controllers\PurchaseFormController@purchaseFormReject');
    Route::put('/purchaseform/review', 'App\Http\Controllers\PurchaseFormController@purchaseFormReview');
    Route::put('/purchaseform/refuse', 'App\Http\Controllers\PurchaseFormController@purchaseFormRefuse');
    Route::get('/vendorbill/purchaseformlist/{id}', 'App\Http\Controllers\PurchaseFormController@fetchpurchaseFormList'); // this purchase form list is used in vendor bill

    //Income Section Billable Item
    Route::get('/billable-note/list', 'App\Http\Controllers\BillableNoteController@list');
    Route::get('/billable-note/add_billable/{id}', 'App\Http\Controllers\BillableNoteController@billableNoteDetail');
    Route::post('/billable-note/add_billable', 'App\Http\Controllers\BillableNoteController@addBillableNote');
    Route::delete('/billable-note/delete_billable/{id}', 'App\Http\Controllers\BillableNoteController@deleteBillableNote');
    Route::put('/billable-note/add_billable/{id}', 'App\Http\Controllers\BillableNoteController@updateBillableNote');

    Route::get('/billable-note/get_billing_period', 'App\Http\Controllers\BillableNoteController@getBillingPeriods');

    /** MEMBERS APIs **/
    Route::get('/member/list', 'App\Http\Controllers\DCOController@allottees');
    Route::get('/member/list/gate', 'App\Http\Controllers\DCOController@allottees');
    Route::get('/member/list/{id}', 'App\Http\Controllers\DCOController@unitAllottees');
    Route::delete('/member/delete/{id}', 'App\Http\Controllers\MemberController@deleteMember');
    Route::post('/member/sendInvitation/{id}', 'App\Http\Controllers\DCOController@sendInvitation');
    Route::get('/member/getBuildingUnitDetails', 'App\Http\Controllers\CommonController@buildingunit');
    Route::get('/member/getBuildingFloorDetails', 'App\Http\Controllers\CommonController@buildingfloor');
    Route::get('/member/getMemberTypeDetails', 'App\Http\Controllers\CommonController@membertype');
    Route::get('/member/memberContactDetailReportPrint', 'App\Http\Controllers\MemberController@contactDetail');
    Route::get('/member/memberContactDetailReportPrint/download/{type}', 'App\Http\Controllers\MemberController@contactDetailDownloadReport');
    Route::get('/member/memberSignatureListReport', 'App\Http\Controllers\MemberController@memberSignature');
    Route::get('/member/tenantsSignatureListReport', 'App\Http\Controllers\MemberController@tenantSignature');
    Route::get('/member/memberSignatureListReport/download/{type}', 'App\Http\Controllers\MemberController@memberSignatureDownload');
    Route::get('/member/tenantsSignatureListReport/download/{type}', 'App\Http\Controllers\MemberController@tenantSignatureDownload');
    Route::get('/member/viewDetails/{id}', 'App\Http\Controllers\MemberController@allotteesViewDetails');
    Route::get('/member/viewDetails/card/{id}', 'App\Http\Controllers\MemberController@allotteesViewDetailsCard');
    Route::get('/member/register/{id}', 'App\Http\Controllers\MemberController@allotteesViewDetailsCard');
    Route::put('/member/register/{id}', 'App\Http\Controllers\MemberController@updateAllottees');
    Route::post('/member/register', 'App\Http\Controllers\MemberController@createAllottees');
    Route::post('/member/bulkAdd', 'App\Http\Controllers\MemberController@bulkAddAllottees');
    Route::get('/membertype/details', 'App\Http\Controllers\DCOController@allotteeType');
    Route::post('/membertype/details', 'App\Http\Controllers\DCOController@createAllotteeType');
    Route::put('/membertype/details/{id}', 'App\Http\Controllers\DCOController@updateAllotteeType');
    Route::delete('/membertype/details/{id}', 'App\Http\Controllers\DCOController@deleteAllotteeType');
    Route::get('/member/jRegisterReportPrint', 'App\Http\Controllers\MemberController@jRegisterReportPrint');
    Route::get('/member/jRegisterReportPrint/download/{type}', 'App\Http\Controllers\MemberController@jRegisterReportPrintDownload');
    Route::get('/member/iRegister/{unitId}', 'App\Http\Controllers\MemberController@iRegister');
    Route::post('/member/downloadMembers/{type}', 'App\Http\Controllers\MemberController@downloadMembers');
    Route::put('/member/list/{type}', 'App\Http\Controllers\MemberController@activateDeactivate');
    Route::post('/member/sharecertificate/{unit_id}', 'App\\Http\\Controllers\\MemberController@addShareCertificate');
    Route::put('/member/sharecertificate/{unit_id}/{share_certificate_id}', 'App\\Http\\Controllers\\MemberController@updateShareCertificate');
    Route::get('/member/unitWiseMembersList', 'App\Http\Controllers\DCOController@unitWiseMembersList');
    Route::get('/member/viewMemberShares/{id}', 'App\Http\Controllers\MemberController@viewMemberShares');
    Route::put('/member/unlinkUser/{member_id}', 'App\Http\Controllers\DCOController@unlinkUser');
    // update member user_id for particular member only used for onegate
    Route::put('/member/{id}', 'App\Http\Controllers\MemberController@updateMemberUserId');

    /** VENDORS & VENDORBILL APIs **/
    Route::get('/vendor/vendorAging', 'App\Http\Controllers\VendorsController@vendorAgingReport');
    Route::get('/vendor/viewVendor', 'App\Http\Controllers\VendorsController@viewVendor');
    Route::get('/vendor/viewVendorDropdown', 'App\Http\Controllers\VendorsController@viewVendorDropdown');
    Route::get('/vendor/viewVendorDetails/{id}', 'App\Http\Controllers\VendorsController@viewVendorDetails');
    Route::get('/vendors/viewVendor', 'App\Http\Controllers\CommonController@activevendors');
    Route::get('/vendorbill/expensePaymentTrackerList', 'App\Http\Controllers\VendorBillController@ExpensePaymentTracker');
    Route::get('vendorbill/viewExpensePaymentVoucherReciept/{id}', 'App\Http\Controllers\VendorBillController@viewExpensePaymentVoucherReciept');
    Route::get('vendorbill/viewExpensePaymentVoucherReciept/{id}/download/{type}', 'App\Http\Controllers\VendorBillController@viewExpensePaymentVoucherRecieptDownload');
    Route::get('/vendorbill/vendorBill', 'App\Http\Controllers\VendorBillController@VendorBillList');
    Route::get('/vendorbill/vendorBillDetails/{id}', 'App\Http\Controllers\VendorBillController@vendorBillDetails');
    Route::get('/vendorbill/vendorBillDetailsCard/{id}', 'App\Http\Controllers\VendorBillController@vendorBillDetailsCard');
    Route::get('/vendorbill/viewPayments/{id}', 'App\Http\Controllers\VendorBillController@ViewPayments');
    Route::get('/vendorbill/vendorAdvances', 'App\Http\Controllers\ExpensesController@vendorAdvances');
    Route::get('/vendorbill/viewAllTransactionsVendors/{type}/{id}', 'App\Http\Controllers\CreditAccountsController@viewMemberTransactions');
    Route::get('/vendorbill/tdspayableReport', 'App\Http\Controllers\ReportsController@tdspayableReport');
    Route::get('/vendorbill/getExpenseLimit', 'App\Http\Controllers\CommonController@expensetracker');
    Route::get('/vendorbill/quickPay', 'App\Http\Controllers\CommonController@paymentmodes');
    Route::get('/vendor/vendorBillList/{id}', 'App\Http\Controllers\VendorsController@vendorBillList');
    Route::get('/vendor/vendorPaymentList/{id}', 'App\Http\Controllers\VendorsController@vendorPaymentList');
    Route::get('/vendorbill/billableVendorBills', 'App\Http\Controllers\VendorBillController@billableVendorBills');
    Route::post('/vendor/addVendor', 'App\Http\Controllers\VendorsController@addVendor');
    Route::get('/common/getVendorAccounts', 'App\Http\Controllers\CommonController@vendorAccountsList');
    Route::get('/common/gstRates', 'App\Http\Controllers\CommonController@gstRatesMaster');
    Route::post('/vendor/viewVendor/download/{type}', 'App\Http\Controllers\VendorsController@downloadVendor');
    Route::put('/vendor/editVendorDetails/{id}', 'App\Http\Controllers\VendorsController@editVendorDetails');
    Route::get('/vendor/editVendorDetails/{id}', 'App\Http\Controllers\VendorsController@fecthDataEditVendorDetails');
    Route::post('/vendorbill/addBill', 'App\Http\Controllers\VendorBillController@newVendorCashPurchase');
    Route::put('/vendorbill/addOpeningBalanceVendor/{id}', 'App\Http\Controllers\VendorBillController@addOpeningBalanceVendor');
    // Route::post('/vendorbill/addCashPurchase', 'App\Http\Controllers\VendorBillController@newVendorCashPurchase'); // old route
    Route::post('/vendorbill/getVendorLedger', 'App\Http\Controllers\VendorBillController@getVendorLedger');
    //Route::post('/vendorbill/addParticular', 'App\Http\Controllers\VendorBillController@addParticular');
    Route::delete('/vendorbill/cancelVendorBill/{id}', 'App\Http\Controllers\ExpensesController@cancelVendorBill');
    Route::get('/vendorbill/fetchDataAddVendorPayment/{ledger}', 'App\Http\Controllers\VendorBillController@fetchDataAddVendorPayment');
    Route::get('/vendorbill/fetchDataVendorPaymentList/{id}', 'App\Http\Controllers\VendorBillController@fetchDataVendorPaymentList'); // not used, now paybill api is getting used.
    Route::put('/vendorbill/paymentTrackerConfirmation/{status}/{id}', 'App\Http\Controllers\VendorBillController@paymentTrackerConfirmation');
    Route::post('/vendorbill/newBillPayment', 'App\Http\Controllers\VendorBillController@newBillPayment');
    Route::put('/vendorbill/newBillPayment/{id}', 'App\Http\Controllers\VendorBillController@newBillPayments');
    Route::put('/vendorbill/changeSingleStatus/{id}/{status}', 'App\Http\Controllers\ExpensesController@changeVendorBillStatus');
    Route::post('/vendorbill/addVendorBill', 'App\Http\Controllers\VendorBillController@addVendorBill');
    Route::put('/vendorbill/editVendorBill/{id}', 'App\Http\Controllers\VendorBillController@editVendorBill');
    Route::get('/vendorbill/getPurChaceDetails', 'App\Http\Controllers\VendorBillController@getPurChaceDetails');
    Route::get('/vendorbill/getVendorDetail', 'App\Http\Controllers\VendorBillController@getVendorDetail');
    Route::get('/vendorbill/fetchDataPaymentTrackerConfirmationReversal/{id}', 'App\Http\Controllers\VendorBillController@fetchDataPaymentTrackerConfirmationReversal');
    Route::get('/vendorbill/updatePaymentTracker/{id}', 'App\Http\Controllers\VendorBillController@updatePaymentTracker');
    //Route::get('/vendorbill/payBill/{vendor_id}/{voucher_id}', 'App\Http\Controllers\VendorBillController@paybill');
    Route::get('/vendorbill/payBill/{vendor_id}', 'App\Http\Controllers\VendorBillController@paybill');
    Route::put('/vendorbill/updatePaymentTracker/{id}', 'App\Http\Controllers\VendorBillController@updateExpensePaymentTracker');
    //Route::put('/vendorbill/add_billable/{id}', 'App\Http\Controllers\VendorBillController@editBillable'); // need to work on this in income section part
    Route::get('/vendorbill/fetchDataVendorAdvance/{id}', 'App\Http\Controllers\VendorBillController@fetchDataVendorAdvance');
    Route::post('/vendorbill/add_billable/{id}', 'App\Http\Controllers\VendorBillController@addBillable');
    Route::get('/vendorbill/editVendorBill/{id}', 'App\Http\Controllers\VendorBillController@editVendorBillGetById');
    Route::get('/vendorbill/add_billable/{id}', 'App\Http\Controllers\VendorBillController@fetchBillableVendorBillsAmount');

    /** ALBUM API **/
    Route::get('/albums/list', 'App\Http\Controllers\AlbumsController@albumsList');
    Route::get('/albums/photos/{id}', 'App\Http\Controllers\AlbumsController@albumView');
    Route::delete('/albums/photos/{id}', 'App\Http\Controllers\AlbumsController@deleteAlbum');

    /** REPORTS API **/
    Route::get('/credit-accounts/nonmemberCreditNote', 'App\Http\Controllers\ReportsController@nonMemberAdvances');
    Route::get('/credit-accounts/memberCreditNote', 'App\Http\Controllers\ReportsController@memberAdvances');
    Route::get(
        '/parking-allotments/parkingAllottmentDetailReportPrint',
        'App\Http\Controllers\ReportsController@parkingAllottmentDetailReportPrint'
    );
    Route::get('/parking-allotments/allottedVehicleDetailReportPrint', 'App\Http\Controllers\ReportsController@allottedVehicleDetailReportPrint');
    Route::get('/parking-allotments/allottedVehicleCountDetailReport', 'App\Http\Controllers\ReportsController@allottedVehicleCountDetailReport');
    Route::get('/parking-allotments/allottedVehicleCountDetailReport/download/{type}', 'App\Http\Controllers\ReportsController@allottedVehicleCountDetailReportDownload');
    Route::get('/parking-allotments/allottedVehicleDetailReportPrint/download/{type}', 'App\Http\Controllers\ReportsController@allottedVehicleDetailReportPrintDownload');
    Route::get('/credit-accounts/nonmemberCreditNote/download/{type}', 'App\Http\Controllers\ReportsController@nonMemberAdvancesDownload');
    Route::get('/credit-accounts/memberCreditNote/download/{type}', 'App\Http\Controllers\ReportsController@memberAdvancesDownload');
    Route::get('/parking-allotments/parkingAllottmentDetailReportPrint/download/{type}', 'App\Http\Controllers\ReportsController@parkingAllottmentDetailReportPrintDownload');
    Route::get('/transaction/voucherReport', 'App\Http\Controllers\ReportsController@voucherReport');
    Route::get('/transaction/voucherReport/download/{type}', 'App\Http\Controllers\ReportsController@voucherReportDownload');
    Route::get('/income-details/receiptReport', 'App\Http\Controllers\ReportsController@receiptReport');
    Route::get('/income-details/receiptReport/download/{type}', 'App\Http\Controllers\ReportsController@downloadReceiptReport');
    Route::get('/accounts/gstReports', 'App\Http\Controllers\ReportsController@gstReport');
    Route::get('/accounts/gstReports/download/{type}', 'App\Http\Controllers\ReportsController@downloadGstReport');
    Route::get('/reports/bankReconciliationReport', 'App\Http\Controllers\ReportsController@bankReconciliationReport');
    Route::post('/reports/bankReconciliationReport/download/{type}', 'App\Http\Controllers\ReportsController@downloadBankReconciliationReport');
    Route::get('/reports/tdsPayable', 'App\Http\Controllers\ReportsController@tdspayableReport');
    Route::post('/reports/tdsPayable/download/{type}', 'App\Http\Controllers\ReportsController@downloadTdsPayableReport');
    Route::get('/income-details/membersReceivableReport', 'App\Http\Controllers\IncomeDetailsController@membersReceivableReport');
    Route::get('/income-details/membersReceivableReport/download/{type}', 'App\Http\Controllers\IncomeDetailsController@membersReceivableReportDownload');
    Route::get('/income-details/membersUnitStatementReport', 'App\Http\Controllers\IncomeDetailsController@membersUnitStatementReport');
    Route::get('/income-details/membersUnitStatementDownloadReport/download/{type}', 'App\Http\Controllers\IncomeDetailsController@membersUnitStatementDownloadReport');

    Route::get('income-details/incidentalReceivableReport', 'App\Http\Controllers\ReportsController@incidentalReceivableReport');
    Route::get('income-details/incidentalReceivableReport/download/{type}', 'App\Http\Controllers\ReportsController@incidentalReceivableReportDownload');
    Route::get('/income-details/membersInvoiceDetailReport', 'App\Http\Controllers\IncomeDetailsController@memberInvoiceDetailReport');
    Route::get('/income-details/membersInvoiceDetailReport/download/{type}', 'App\Http\Controllers\IncomeDetailsController@memberInvoiceDetailReportDownload');
    Route::get('/income-details/incidentalInvoiceDetailReport', 'App\Http\Controllers\IncomeDetailsController@incidentalInvoiceDetailReport');
    Route::get('/income-details/incidentalInvoiceDetailReport/download/{type}', 'App\Http\Controllers\IncomeDetailsController@incidentalInvoiceDetailReportDownload');
    
    Route::get('/reports/expenseReport', 'App\Http\Controllers\ReportsController@expenseReport');
    Route::get('/reports/expenseReport/download/{type}', 'App\Http\Controllers\ReportsController@expenseReportDownload');

    Route::get('/reports/expensePaymentReport', 'App\Http\Controllers\ReportsController@expensePaymentReport');
    Route::get('/reports/expensePaymentReport/download/{type}', 'App\Http\Controllers\ReportsController@expensePaymentDownloadReport');

    Route::get('/reports/expenseBudgetReport', 'App\Http\Controllers\ReportsController@expenseBudgetReport');
    Route::get('/reports/expenseBudgetReport/download/{type}', 'App\Http\Controllers\ReportsController@downloadExpenseBudgetReport');

    Route::get('/reports/bankRecoReport', 'App\Http\Controllers\ReportsController@bankRecoReport');
    Route::get('/reports/bankRecoReport/download/{type}', 'App\Http\Controllers\ReportsController@bankRecoReportDownload');

    /** MEMBERS RECEIVABLE APIs **/
    Route::get('/reports/members-receivable', 'App\Http\Controllers\MembersReceivableController@index');
    Route::get('/reports/members-receivable/{id}', 'App\Http\Controllers\MembersReceivableController@show');

    /** HELPDESK API  **/
    Route::get('/helpdesk/helpdeskReport', 'App\Http\Controllers\HelpdeskController@helpdeskReport');
    Route::get('/helpdesk/helpdeskReport/download/{type}', 'App\Http\Controllers\HelpdeskController@helpdeskReportDownload');

    Route::get('/helpdesk/issues', 'App\Http\Controllers\HelpdeskController@issueList');
    Route::get('/helpdesk/first_body/{issue_id}', 'App\Http\Controllers\HelpdeskController@getFirstBody');
    Route::post('/helpdesk/add', 'App\Http\Controllers\HelpdeskController@issueAdd');
    Route::put('/helpdesk/add/{issue_id}', 'App\Http\Controllers\HelpdeskController@issueEdit');
    Route::patch('/helpdesk/bulk/{status}', 'App\Http\Controllers\HelpdeskController@issueBulkEdit');
    Route::delete('/helpdesk/bulk', 'App\Http\Controllers\HelpdeskController@issueBulkEdit');
    Route::delete('/helpdesk/delete_issue/{issue_id}', 'App\Http\Controllers\HelpdeskController@issueDelete');

    Route::get('/helpdesk/view_issue/{issue_id}/assign_job_card', 'App\Http\Controllers\HelpdeskController@issueDetailAssignJobCardList');
    Route::post('/helpdesk/view_issue/{issue_id}/assign_job_card', 'App\Http\Controllers\HelpdeskController@issueDetailAssignJobCardAdd');
    Route::put('/helpdesk/view_issue/{issue_id}/assign_job_card/{job_id}', 'App\Http\Controllers\HelpdeskController@issueDetailAssignJobCardEdit');
    Route::get('/helpdesk/view_issue/{issue_id}/assign_job_card/{job_id}', 'App\Http\Controllers\HelpdeskController@issueDetailAssignJobCardDetail');
    Route::get('/helpdesk/jobcart/{issue_id}/{job_id}/download/{type}', 'App\Http\Controllers\HelpdeskController@jobcartDownload');

    // PATCH: Assign Help Topic to Issue
    Route::patch('/helpdesk/view_issue/{issue_id}', 'App\Http\Controllers\HelpdeskController@issueDetailAssignHelpTopic');

    Route::get('/helpdesk/view_issue/{issue_id}/ticket_details', 'App\Http\Controllers\HelpdeskController@issueDetailTicketDetail');
    Route::get('/helpdesk/view_issue/{issue_id}/communication', 'App\Http\Controllers\HelpdeskController@issueDetailCommunicationList');
    Route::get('/helpdesk/view_issue/{issue_id}/member_details', 'App\Http\Controllers\HelpdeskController@issueDetailMemberDetail');
    Route::get('/helpdesk/view_issue/{issue_id}/attachments', 'App\Http\Controllers\HelpdeskController@issueDetailAttachment');
    Route::patch('/helpdesk/view_issue/{issue_id}/change_status', 'App\Http\Controllers\HelpdeskController@issueDetailChangeStatus');
    Route::post('/helpdesk/view_issue/{issue_id}/post_internal_note', 'App\Http\Controllers\HelpdeskController@issueDetailPostInternalNote');
    Route::post('/helpdesk/view_issue/{issue_id}/assign_issue', 'App\Http\Controllers\HelpdeskController@issueDetailAssignIssue');
    Route::post('/helpdesk/view_issue/{issue_id}/post_reply', 'App\Http\Controllers\HelpdeskController@issueDetailPostReply');

    Route::get('/helpdeskcannedresponse/cannedresponse', 'App\Http\Controllers\HelpdeskController@cannedResponseList');
    Route::post('/helpdeskcannedresponse/add_cannedresponse', 'App\Http\Controllers\HelpdeskController@cannedResponseAdd');
    Route::get('/helpdeskcannedresponse/view_cannedresponse/{canned_response_id}', 'App\Http\Controllers\HelpdeskController@cannedResponseDetail');
    Route::put('/helpdeskcannedresponse/add_cannedresponse/{canned_response_id}', 'App\Http\Controllers\HelpdeskController@cannedResponseEdit');
    Route::delete('/helpdeskcannedresponse/delete_cannedresponse/{id}', 'App\Http\Controllers\HelpdeskController@cannedResponseDelete');

    Route::get('/helpdesktopics/list_escalations', 'App\Http\Controllers\HelpdeskController@escalationList');
    Route::post('/helpdesktopics/add_escalation/', 'App\Http\Controllers\HelpdeskController@escalationAdd');
    Route::get('/helpdesktopics/view_escalation/{help_topic_id}', 'App\Http\Controllers\HelpdeskController@escalationDetail');
    Route::put('/helpdesktopics/add_escalation/{help_topic_id}', 'App\Http\Controllers\HelpdeskController@escalationEdit');
    Route::delete('/helpdesktopics/deleteEscalation/{help_topic_id}', 'App\Http\Controllers\HelpdeskController@escalationDelete');

    Route::get('/helpdesktopics/help_topics', 'App\Http\Controllers\HelpdeskController@helpTopicList');
    Route::get('/helpdesktopics/help_topic_dropdown', 'App\Http\Controllers\HelpdeskController@helpTopicDropdown');
    Route::post('/helpdesktopics/add_helptopic', 'App\Http\Controllers\HelpdeskController@helpTopicAdd');
    Route::get('/helpdesktopics/view_helptopic/{help_topic_id}', 'App\Http\Controllers\HelpdeskController@helpTopicDetail');
    Route::put('/helpdesktopics/add_helptopic/{help_topic_id}', 'App\Http\Controllers\HelpdeskController@helpTopicEdit');
    Route::delete('/helpdesktopics/delete_helptopic/{help_topic_id}', 'App\Http\Controllers\HelpdeskController@helpTopicDelete');

    Route::get('/helpdesk/add/staff_customer_tree', 'App\Http\Controllers\HelpdeskController@staffMemberTree');

    Route::post('/notification', 'App\Http\Controllers\NotificationController@fcmNotification');
    Route::post('/bulk_notification', 'App\Http\Controllers\NotificationController@bulkNotification');

    //Activity Log
    Route::get('/credit-accounts/viewActivityLogs/{type}/{id}/activity_log', 'App\Http\Controllers\CreditAccountsController@viewMongoActivityLogs');
    Route::get('/helpdesktopics/view_escalation/{help_topic_id}/activity_log', 'App\Http\Controllers\HelpdeskController@helpTopicActivityLog');
    Route::get('/helpdesktopics/view_helptopic/{help_topic_id}/activity_log', 'App\Http\Controllers\HelpdeskController@helpTopicActivityLog');
    Route::get('/helpdeskcannedresponse/view_cannedresponse/{canned_response_id}/activity_log', 'App\Http\Controllers\HelpdeskController@cannedResponseActivityLog');
    Route::get('/transaction/addDebitEntry/activity_log', 'App\Http\Controllers\TransactionController@accountMongoLog');
    Route::get('/transaction/addJournalEntry/activity_log', 'App\Http\Controllers\TransactionController@accountMongoLog');
    Route::get('/transaction/addContraEntry/activity_log', 'App\Http\Controllers\TransactionController@accountMongoLog');
    Route::get('/transaction/addPaymentVoucher/activity_log', 'App\Http\Controllers\TransactionController@accountMongoLog');
    Route::get('/transaction/addMultiplePaymentVoucher/activity_log', 'App\Http\Controllers\TransactionController@accountMongoLog');
    Route::get('/income-tracker-setting/readGeneralSettings/activity_log', 'App\Http\Controllers\TransactionController@accountMongoLog');
    Route::get('/income-tracker-setting/readParticularSettings/activity_log', 'App\Http\Controllers\TransactionController@accountMongoLog');
    Route::get('/income-tracker-setting/readIncomeAccounts/activity_log', 'App\Http\Controllers\TransactionController@accountMongoLog');
    Route::get('/societies/viewEmailSmsLog', 'App\Http\Controllers\GeneralSettingsController@emailSmsActivityLog');
    Route::get('/income-tracker-setting/readGeneralSettings/{filename}/{label}', 'App\Http\Controllers\TransactionController@generalSettingsPDF');

    //Activity Log
    Route::post('/mongo_log', 'App\Http\Controllers\CommonController@mongoNotificationLog');
    Route::get('/read_notification', 'App\Http\Controllers\CommonController@readNotification');
    Route::get('/unread_notification_count', 'App\Http\Controllers\CommonController@unreadNotification');

    /* NEW */
    // Member Ledger Unit Statement
    // Route::get('/accountsreporting/membersUnitAccountStatementReport', 'App\Http\Controllers\AccountsReportingController@MemberUnitLedgerStatement'); double route

    // Income Receipt Report
    Route::get('/income-details/memberReceiptReport', 'App\Http\Controllers\IncomeDetailsController@MemberReceiptReport');
    Route::get('/income-details/memberReceiptReport/download/{type}', 'App\Http\Controllers\IncomeDetailsController@downloadMemberReceiptReport');
    Route::put('/income-details/paymentReceipt/sendNotification/{id}', 'App\Http\Controllers\IncomeDetailsController@paymentReceiptSendNotification');

    Route::match(['post', 'put'], '/expensetracker/settingApply', 'App\Http\Controllers\ExpenseSetupController@expenseaddaccount');
    Route::get('/socweb/socprofile', 'App\Http\Controllers\SocWebController@getSocWebsiteDetails');
    Route::post('/socweb/socwebsiteUpdate', 'App\Http\Controllers\SocWebController@updateSocWebsite');

    // Amenities APIs
    Route::get('/amenities', 'App\Http\Controllers\AmenitiesController@amenitiesList');
    Route::post('/amenities', 'App\Http\Controllers\AmenitiesController@amenitiesAdd');
    Route::get('/amenities/{id}', 'App\Http\Controllers\AmenitiesController@amenitiesDetail');
    Route::put('/amenities/{id}', 'App\Http\Controllers\AmenitiesController@amenitiesEdit');
    Route::delete('/amenities/{id}', 'App\Http\Controllers\AmenitiesController@amenitiesDelete');

    // Helpdesk Issue by ID API
    Route::get('/helpdesk/issues/{issue_id}', 'App\Http\Controllers\HelpdeskController@getIssueById');

    // Book Amenities
    Route::post('/amenities/{amenity_id}/bookings', 'App\Http\Controllers\AmenitiesController@amenitiesBook');
    Route::get('/amenities/{amenity_id}/bookings', 'App\Http\Controllers\AmenitiesController@getSlots');
    Route::get('/users/{user_id}/bookings', 'App\Http\Controllers\AmenitiesController@getUserBookings');
    Route::get('/users/{user_id}/amenities/{amenity_id}/bookings', 'App\Http\Controllers\AmenitiesController@getUserBooking');
    Route::patch('/amenities/{amenity_id}/bookings/{booking_id}', 'App\Http\Controllers\AmenitiesController@cancelBooking');
    // Verify booking pin
    Route::post('/amenities/verify-pin', 'App\Http\Controllers\AmenitiesController@verifyBookingPin');

    // Download actions
    Route::get('/income-details/paymentReceipt/download/{id}', 'App\Http\Controllers\DownloadController@paymentReceipt');

    //Dashboard
    Route::get('/admindashboard/index/helpdesk', 'App\Http\Controllers\DashboardController@dashboardHelpdesk');
    Route::get('/admindashboard/index/allottees', 'App\Http\Controllers\DashboardController@dashboardAllottees');
    Route::get('/admindashboard/index/surveys', 'App\Http\Controllers\DashboardController@dashboardSurveys');
    Route::get('/admindashboard/index/balance_dues', 'App\Http\Controllers\DashboardController@dashboardBalanceDues');
    Route::get('/admindashboard/index/expenses', 'App\Http\Controllers\DashboardController@dashboardExpenses');
    Route::get('/admindashboard/index/notifications', 'App\Http\Controllers\DashboardController@dashboardNotifications');
    Route::get('/admindashboard/index/bank_cash_ledger', 'App\Http\Controllers\DashboardController@dashboardBankCashLedger');

    //file-upload
    Route::post('/file-upload', 'App\Http\Controllers\FilesController@upload');

    //Extra APIs
    Route::get('/settings/common_timestamp','App\Http\Controllers\GeneralSettingsController@settingCommonTimestamp');

    /** NON-MEMBER RECEIVABLE REPORT APIs **/
    Route::get('/reports/nonMembersReceivable', 'App\Http\Controllers\ReportsController@nonMembersReceivable');
    Route::get('/reports/nonMembersReceivable/download/{type}', 'App\Http\Controllers\ReportsController@nonMembersReceivableDownload');
});

Route::middleware(['tenant.connection', 'request.prepare'])->group(function (): void {
    Route::get('/test', function (Request $request) {
        Artisan::call('workflow:check', ['input' => json_encode($request->all())]
        );
        return response()->json(Artisan::output());
    });

    Route::get('/test2', function (Request $request) {
        Artisan::call('workflow:incomemember', ['input' => json_encode($request->all())]
        );
        return response()->json(Artisan::output());
    });
});

Route::middleware(['request.prepare'])->group(function (): void {
    Route::get('/user/society/access', 'App\Http\Controllers\UserDetailsController@userSocietyAccess');
});

Route::post('/generate-pdf', [PdfController::class, 'generatePdf']);
Route::post('/generate-screenshot', [PdfController::class, 'generateScreenshot']);
